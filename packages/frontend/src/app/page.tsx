'use client'

import { useState, useEffect } from 'react'
import { apiHelpers } from '@/lib/api'

interface User {
  id: number
  name: string
  email: string
}

interface ApiResponse {
  message?: string
  version?: string
  timestamp?: string
}

export default function Home() {
  const [apiData, setApiData] = useState<ApiResponse | null>(null)
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [newUserName, setNewUserName] = useState('')
  const [newUserEmail, setNewUserEmail] = useState('')
  const [isCreating, setIsCreating] = useState(false)

  const fetchData = async () => {
    try {
      setLoading(true)
      setError(null)

      // Fetch API info using traditional fetch (since it's not part of the RPC API)
      const apiResponse = await fetch('http://localhost:8000/')
      const apiData = await apiResponse.json()
      setApiData(apiData)

      // Fetch users using RPC helper
      const usersData = await apiHelpers.getUsers()
      setUsers(usersData.users || [])

      setLoading(false)
    } catch (err) {
      setError('Failed to connect to backend API')
      setLoading(false)
      console.error('Error fetching data:', err)
    }
  }

  const createUser = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!newUserName.trim() || !newUserEmail.trim()) return

    try {
      setIsCreating(true)

      // Create user using RPC helper with type safety
      const result = await apiHelpers.createUser({
        name: newUserName.trim(),
        email: newUserEmail.trim()
      })

      console.log('User created:', result)

      // Reset form
      setNewUserName('')
      setNewUserEmail('')

      // Refresh users list
      await fetchData()
    } catch (err) {
      console.error('Error creating user:', err)
      setError(err instanceof Error ? err.message : 'Failed to create user')
    } finally {
      setIsCreating(false)
    }
  }

  const deleteUser = async (userId: number) => {
    if (!confirm('Are you sure you want to delete this user?')) return

    try {
      await apiHelpers.deleteUser(userId)
      console.log('User deleted successfully')

      // Refresh users list
      await fetchData()
    } catch (err) {
      console.error('Error deleting user:', err)
      setError(err instanceof Error ? err.message : 'Failed to delete user')
    }
  }

  useEffect(() => {
    fetchData()
  }, [])

  if (loading) {
    return (
      <main className="flex min-h-screen flex-col items-center justify-center p-24">
        <div className="text-xl">Loading...</div>
      </main>
    )
  }

  if (error) {
    return (
      <main className="flex min-h-screen flex-col items-center justify-center p-24">
        <div className="text-xl text-red-500">{error}</div>
        <div className="mt-4 text-sm text-gray-600">
          Make sure the backend server is running on http://localhost:8000
        </div>
      </main>
    )
  }

  return (
    <main className="flex min-h-screen flex-col items-center justify-between p-24">
      <div className="z-10 max-w-5xl w-full items-center justify-between font-mono text-sm lg:flex">
        <p className="fixed left-0 top-0 flex w-full justify-center border-b border-gray-300 bg-gradient-to-b from-zinc-200 pb-6 pt-8 backdrop-blur-2xl dark:border-neutral-800 dark:bg-zinc-800/30 dark:from-inherit lg:static lg:w-auto lg:rounded-xl lg:border lg:bg-gray-200 lg:p-4 lg:dark:bg-zinc-800/30">
          ARB-AIO Frontend&nbsp;
          <code className="font-mono font-bold">Next.js + Hono</code>
        </p>
      </div>

      <div className="relative flex place-items-center">
        <div className="text-center">
          <h1 className="text-4xl font-bold mb-8">Welcome to ARB-AIO</h1>
          
          {apiData && (
            <div className="mb-8 p-6 bg-white/10 rounded-lg backdrop-blur-sm">
              <h2 className="text-2xl font-semibold mb-4">Backend API Status</h2>
              <p className="mb-2">Message: {apiData.message}</p>
              <p className="mb-2">Version: {apiData.version}</p>
              <p className="text-sm text-gray-600">Last updated: {apiData.timestamp}</p>
            </div>
          )}

          {users.length > 0 && (
            <div className="mb-8 p-6 bg-white/10 rounded-lg backdrop-blur-sm">
              <h2 className="text-2xl font-semibold mb-4">Users from RPC API</h2>
              <div className="space-y-2">
                {users.map(user => (
                  <div key={user.id} className="p-3 bg-white/20 rounded flex justify-between items-center">
                    <div>
                      <p className="font-medium">{user.name}</p>
                      <p className="text-sm text-gray-600">{user.email}</p>
                      <p className="text-xs text-gray-500">ID: {user.id}</p>
                    </div>
                    <button
                      onClick={() => deleteUser(user.id)}
                      className="px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-sm rounded transition-colors"
                    >
                      Delete
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Create User Form */}
          <div className="mb-8 p-6 bg-white/10 rounded-lg backdrop-blur-sm">
            <h2 className="text-2xl font-semibold mb-4">Create New User (RPC)</h2>
            <form onSubmit={createUser} className="space-y-4">
              <div>
                <label htmlFor="name" className="block text-sm font-medium mb-1">
                  Name
                </label>
                <input
                  type="text"
                  id="name"
                  value={newUserName}
                  onChange={(e) => setNewUserName(e.target.value)}
                  className="w-full px-3 py-2 bg-white/20 rounded border border-white/30 text-white placeholder-white/60"
                  placeholder="Enter user name"
                  required
                />
              </div>
              <div>
                <label htmlFor="email" className="block text-sm font-medium mb-1">
                  Email
                </label>
                <input
                  type="email"
                  id="email"
                  value={newUserEmail}
                  onChange={(e) => setNewUserEmail(e.target.value)}
                  className="w-full px-3 py-2 bg-white/20 rounded border border-white/30 text-white placeholder-white/60"
                  placeholder="Enter user email"
                  required
                />
              </div>
              <button
                type="submit"
                disabled={isCreating || !newUserName.trim() || !newUserEmail.trim()}
                className="w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed rounded text-white font-medium transition-colors"
              >
                {isCreating ? 'Creating...' : 'Create User'}
              </button>
            </form>
          </div>
        </div>
      </div>

      <div className="mb-32 grid text-center lg:max-w-5xl lg:w-full lg:mb-0 lg:grid-cols-4 lg:text-left">
        <a
          href="https://nextjs.org/docs"
          className="group rounded-lg border border-transparent px-5 py-4 transition-colors hover:border-gray-300 hover:bg-gray-100 hover:dark:border-neutral-700 hover:dark:bg-neutral-800/30"
          target="_blank"
          rel="noopener noreferrer"
        >
          <h2 className="mb-3 text-2xl font-semibold">
            Next.js{' '}
            <span className="inline-block transition-transform group-hover:translate-x-1 motion-reduce:transform-none">
              -&gt;
            </span>
          </h2>
          <p className="m-0 max-w-[30ch] text-sm opacity-50">
            Find in-depth information about Next.js features and API.
          </p>
        </a>

        <a
          href="https://hono.dev"
          className="group rounded-lg border border-transparent px-5 py-4 transition-colors hover:border-gray-300 hover:bg-gray-100 hover:dark:border-neutral-700 hover:dark:bg-neutral-800/30"
          target="_blank"
          rel="noopener noreferrer"
        >
          <h2 className="mb-3 text-2xl font-semibold">
            Hono{' '}
            <span className="inline-block transition-transform group-hover:translate-x-1 motion-reduce:transform-none">
              -&gt;
            </span>
          </h2>
          <p className="m-0 max-w-[30ch] text-sm opacity-50">
            Learn about Hono, the fast web framework for the edge.
          </p>
        </a>

        <a
          href="https://bun.sh"
          className="group rounded-lg border border-transparent px-5 py-4 transition-colors hover:border-gray-300 hover:bg-gray-100 hover:dark:border-neutral-700 hover:dark:bg-neutral-800/30"
          target="_blank"
          rel="noopener noreferrer"
        >
          <h2 className="mb-3 text-2xl font-semibold">
            Bun{' '}
            <span className="inline-block transition-transform group-hover:translate-x-1 motion-reduce:transform-none">
              -&gt;
            </span>
          </h2>
          <p className="m-0 max-w-[30ch] text-sm opacity-50">
            Explore Bun, the fast all-in-one JavaScript runtime.
          </p>
        </a>

        <a
          href="http://localhost:8000"
          className="group rounded-lg border border-transparent px-5 py-4 transition-colors hover:border-gray-300 hover:bg-gray-100 hover:dark:border-neutral-700 hover:dark:bg-neutral-800/30"
          target="_blank"
          rel="noopener noreferrer"
        >
          <h2 className="mb-3 text-2xl font-semibold">
            API{' '}
            <span className="inline-block transition-transform group-hover:translate-x-1 motion-reduce:transform-none">
              -&gt;
            </span>
          </h2>
          <p className="m-0 max-w-[30ch] text-sm opacity-50">
            Access the backend API directly.
          </p>
        </a>
      </div>
    </main>
  )
}
