# ARB-AIO Frontend

A Next.js frontend application for the ARB-AIO project.

## Features

- Built with [Next.js 14](https://nextjs.org/) with App Router
- [TypeScript](https://www.typescriptlang.org/) for type safety
- [Tailwind CSS](https://tailwindcss.com/) for styling
- API integration with Hono backend
- Responsive design
- ESLint for code quality

## Getting Started

### Development

```bash
# Install dependencies
bun install

# Start development server
bun run dev

# Open http://localhost:3000 in your browser
```

### Build

```bash
# Build for production
bun run build

# Start production server
bun run start
```

### Linting

```bash
# Run ESLint
bun run lint

# Type checking
bun run type-check
```

## Project Structure

```
src/
├── app/                 # App Router pages and layouts
│   ├── globals.css     # Global styles
│   ├── layout.tsx      # Root layout
│   └── page.tsx        # Home page
├── components/         # Reusable components
└── lib/               # Utility functions
```

## API Integration

The frontend is configured to communicate with the Hono backend running on `http://localhost:8000`. The Next.js configuration includes API rewrites for seamless integration.

## Tech Stack

- [Next.js 14](https://nextjs.org/) - React framework
- [React 18](https://reactjs.org/) - UI library
- [TypeScript](https://www.typescriptlang.org/) - Type safety
- [Tailwind CSS](https://tailwindcss.com/) - Styling
- [Bun](https://bun.sh/) - Package manager and runtime
