import { hc } from 'hono/client'
import type { AppType } from '@arb-aio/backend/src/index'

// Create RPC client with base URL
const baseUrl =  'http://localhost:8000'

// Create typed client
const client = hc<AppType>(baseUrl)


// Helper functions for common operations
export const apiHelpers = {
  // Get all users with error handling
  async getUsers() {
    try {
      const response = await client.users.$get()
      if (response.ok) {
        return await response.json()
      }
      throw new Error(`Failed to fetch users: ${response.status}`)
    } catch (error) {
      console.error('Error fetching users:', error)
      throw error
    }
  },

  // Get user by ID with error handling
  async getUser(id: number) {
    try {
      const response = await client.users[':id'].$get({
        param: { id: id.toString() }
      })
      if (response.ok) {
        return await response.json()
      }
      throw new Error(`Failed to fetch user: ${response.status}`)
    } catch (error) {
      console.error('Error fetching user:', error)
      throw error
    }
  },

  // Create user with error handling
  async createUser(userData: { name: string; email: string }) {
    try {
      const response = await client.users.$post({
        json: userData
      })
      if (response.ok) {
        return await response.json()
      }
      const errorData = await response.json()
      throw new Error(`Failed to create user: ${JSON.stringify(errorData)}`)
    } catch (error) {
      console.error('Error creating user:', error)
      throw error
    }
  },

  // Update user with error handling
  async updateUser(id: number, userData: { name: string; email: string }) {
    try {
      const response = await client.users[':id'].$put({
        param: { id: id.toString() },
        json: userData
      })
      if (response.ok) {
        return await response.json()
      }
      const errorData = await response.json()
      throw new Error(`Failed to update user: ${JSON.stringify(errorData)}`)
    } catch (error) {
      console.error('Error updating user:', error)
      throw error
    }
  },

  // Delete user with error handling
  async deleteUser(id: number) {
    try {
      const response = await client.users[':id'].$delete({
        param: { id: id.toString() }
      })
      if (response.ok) {
        return await response.json()
      }
      const errorData = await response.json()
      throw new Error(`Failed to delete user: ${JSON.stringify(errorData)}`)
    } catch (error) {
      console.error('Error deleting user:', error)
      throw error
    }
  }
}
