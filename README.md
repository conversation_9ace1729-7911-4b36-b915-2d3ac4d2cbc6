# ARB-AIO

A Bun monorepo project with Hono backend and Next.js frontend.

## Project Structure

```
arb-aio/
├── packages/
│   ├── backend/          # Hono API server
│   ├── frontend/         # Next.js web application
│   ├── a/               # Legacy package A
│   └── b/               # Legacy package B
├── package.json         # Root workspace configuration
└── README.md           # This file
```

## Quick Start

### Prerequisites

- [Bun](https://bun.sh/) installed on your system

### Installation

```bash
# Install all dependencies
bun run install:all
```

### Development

```bash
# Start both backend and frontend in development mode
bun run dev

# Or start them individually:
bun run dev:backend    # Starts Hono server on http://localhost:8000
bun run dev:frontend   # Starts Next.js on http://localhost:3000
```

### Production Build

```bash
# Build both projects
bun run build

# Start both in production mode
bun run start
```

### Individual Package Commands

```bash
# Backend (Hono)
cd packages/backend
bun install
bun run dev          # Development server
bun run build        # Build for production
bun run start        # Production server

# Frontend (Next.js)
cd packages/frontend
bun install
bun run dev          # Development server
bun run build        # Build for production
bun run start        # Production server
```

## Tech Stack

### Backend
- **[Hono](https://hono.dev/)** - Fast, lightweight web framework
- **[Hono RPC](https://hono.dev/docs/guides/rpc)** - Type-safe RPC communication
- **[Zod](https://zod.dev/)** - Schema validation
- **[Bun](https://bun.sh/)** - JavaScript runtime and package manager
- **TypeScript** - Type safety

### Frontend
- **[Next.js 14](https://nextjs.org/)** - React framework with App Router
- **[React 18](https://reactjs.org/)** - UI library
- **[Hono Client](https://hono.dev/docs/guides/rpc#client)** - Type-safe API client
- **[Tailwind CSS](https://tailwindcss.com/)** - Utility-first CSS framework
- **TypeScript** - Type safety

## API Endpoints

The backend provides the following RPC endpoints:

- `GET /` - Welcome message and API info
- `GET /health` - Health check
- `GET /api/users` - Get all users
- `GET /api/users/:id` - Get user by ID
- `POST /api/users` - Create a new user (with Zod validation)
- `PUT /api/users/:id` - Update user by ID
- `DELETE /api/users/:id` - Delete user by ID

## RPC Features

- **Type Safety**: Full TypeScript support from backend to frontend
- **Schema Validation**: Zod schemas for request/response validation
- **Auto-completion**: IDE support for API calls with IntelliSense
- **Error Handling**: Structured error responses with proper HTTP status codes
- **Client Helpers**: Convenient wrapper functions for common operations

## Development Notes

- **Hono RPC**: Type-safe communication between frontend and backend
- **Workspace Dependencies**: Frontend imports backend types directly
- **Schema Validation**: All API inputs are validated using Zod schemas
- **Hot Reload**: Both development servers support hot reloading
- **CORS**: Configured for seamless frontend-backend communication
- **Error Boundaries**: Proper error handling throughout the application

## Scripts

- `bun run dev` - Start both backend and frontend in development mode
- `bun run build` - Build both projects for production
- `bun run start` - Start both projects in production mode
- `bun run clean` - Clean all node_modules and lock files
- `bun run install:all` - Install dependencies for all packages
